"use client";

import { useState, useRef } from "react";
import { useUser } from "@stackframe/stack";
import { Task, TaskSortOption } from "@/lib/db";
import { useReorderTasksMutation } from "@/lib/queries";
import { TaskItem } from "./task-item";
import { QuickAddTask } from "./quick-add-task";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  TouchSensor,
  MouseSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverEvent,
  DragOverlay,
  MeasuringStrategy,
} from "@dnd-kit/core";
import {
  restrictToVerticalAxis,
} from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  AnimateLayoutChanges,
} from "@dnd-kit/sortable";

interface TaskListProps {
  tasks: Task[]; // Parent tasks only (filtered)
  allTasks?: Task[]; // All tasks including subtasks (for TaskItem to find its subtasks)
  onTaskUpdated: (updatedTask?: Task, statusChanged?: boolean) => void;
  onTaskDeleted: (deletedTaskId?: string) => void;
  onTasksReordered: (tasks: Task[]) => void;
  sortOption: TaskSortOption;
  listId: string;
  listColor?: string | null;
  taskCounts?: Record<string, number>;
  taskMode?: "completion" | "selection";
  selectedTaskIds?: Set<string>;
  onTaskSelectionChange?: (selectedIds: Set<string>) => void;
  lastSelectedTaskId?: string | null;
  showQuickAdd?: boolean;
  onAddTaskClick?: () => void;
  isInlineEditEnabled?: boolean;
  activeActionIconsTaskId?: string | null;
  onActionIconsChange?: (taskId: string | null) => void;
  onNavigateToTask?: (task: Task) => void; // Callback to switch to a different task
  isTagFiltered?: boolean; // Whether we're in tag-filtered view
}

export function TaskList({
  tasks,
  allTasks,
  onTaskUpdated,
  onTaskDeleted,
  onTasksReordered,
  sortOption,
  listId,
  listColor,
  taskCounts,
  taskMode = "completion",
  selectedTaskIds = new Set(),
  onTaskSelectionChange,
  lastSelectedTaskId = null,
  showQuickAdd = false,
  onAddTaskClick,
  isInlineEditEnabled = true,
  activeActionIconsTaskId = null,
  onActionIconsChange,
  onNavigateToTask,
  isTagFiltered = false,
}: TaskListProps) {
  const user = useUser();
  const [items, setItems] = useState(tasks);
  const [isDragging, setIsDragging] = useState(false);
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const lastDragOverRef = useRef<string | null>(null);
  const isReorderingRef = useRef(false);

  // TanStack Query mutation for reordering
  const reorderTasksMutation = useReorderTasksMutation(listId, sortOption);

  // Update local state when tasks prop changes, but not during or immediately after reordering
  if (!isReorderingRef.current && JSON.stringify(tasks) !== JSON.stringify(items)) {
    setItems(tasks);
  }

  // Optimized sensors for both desktop and mobile
  const sensors = useSensors(
    // Mouse sensor for desktop
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8, // Require 8px movement to start drag (allows for scrolling)
      },
    }),
    // Touch sensor for mobile with press-and-hold
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250, // 250ms press-and-hold to start drag
        tolerance: 5, // Allow 5px of movement during the delay
      },
    }),
    // Keyboard sensor for accessibility
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Disable all animations for instant positioning
  const measuring = {
    droppable: {
      strategy: MeasuringStrategy.Always,
    },
  };

  // Disable layout animations completely for instant positioning
  const animateLayoutChanges: AnimateLayoutChanges = () => false;



  const handleDragStart = (event: DragStartEvent) => {
    setIsDragging(true);
    isReorderingRef.current = true; // Prevent prop updates during drag
    lastDragOverRef.current = null; // Reset drag over tracking

    // Find and set the active task for the drag overlay
    const draggedTask = items.find(task => task.id === event.active.id);
    setActiveTask(draggedTask || null);

    // Add haptic feedback on mobile if available
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;

    // Only provide haptic feedback when moving to a new target
    if (over && over.id !== lastDragOverRef.current) {
      lastDragOverRef.current = over.id as string;

      // Add haptic feedback when dragging over a new task
      if ('vibrate' in navigator) {
        navigator.vibrate(30);
      }
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    // Immediately clear drag state for instant response
    setIsDragging(false);
    setActiveTask(null);
    lastDragOverRef.current = null; // Reset drag over tracking

    if (over && active.id !== over.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over.id);

      const newItems = arrayMove(items, oldIndex, newIndex);
      setItems(newItems);
      onTasksReordered(newItems);

      // Update positions in the database using TanStack Query
      // Use mutate instead of mutateAsync to avoid waiting for the response
      if (user) {
        const taskIds = newItems.map((task) => task.id);
        reorderTasksMutation.mutate(
          { userId: user.id, taskIds },
          {
            onError: (error) => {
              console.error("Error reordering tasks:", error);
              // Revert optimistic update on error
              setItems(tasks);
              // Reset reordering flag on error
              isReorderingRef.current = false;
            },
            onSettled: () => {
              // Allow prop updates again after a short delay to prevent visual jumps
              setTimeout(() => {
                isReorderingRef.current = false;
              }, 100);
            }
          }
        );
      }
    } else {
      // Reset reordering flag immediately if no reordering occurred
      isReorderingRef.current = false;
    }
  };

  return (
    <div className={`space-y-4 ${isDragging ? 'dnd-context-dragging' : ''}`}>
      {sortOption === "position" ? (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToVerticalAxis]}
          measuring={measuring}
        >
          <SortableContext
            items={items.map((task) => task.id)}
            strategy={verticalListSortingStrategy}
          >
            {items.map((task) => (
              <TaskItem
                key={task.id}
                task={task}
                onUpdated={onTaskUpdated}
                onDeleted={onTaskDeleted}
                isDraggable={true}
                isAnyTaskDragging={isDragging}
                listId={listId}
                sortOption={sortOption}
                listColor={listColor}
                taskCounts={taskCounts}
                taskMode={taskMode}
                selectedTaskIds={selectedTaskIds}
                onTaskSelectionChange={onTaskSelectionChange}
                lastSelectedTaskId={lastSelectedTaskId}
                isInlineEditEnabled={isInlineEditEnabled}
                activeActionIconsTaskId={activeActionIconsTaskId}
                onActionIconsChange={onActionIconsChange}
                allTasks={allTasks || tasks}
                onNavigateToTask={onNavigateToTask}
                isTagFiltered={isTagFiltered}
              />
            ))}
          </SortableContext>

          {/* Drag Overlay to prevent card compression and maintain visual consistency */}
          <DragOverlay
            adjustScale={false}
            dropAnimation={null}
            style={{
              transformOrigin: '0 0',
              transition: 'none',
            }}
          >
            {activeTask ? (
              <div className="drag-overlay">
                <TaskItem
                  task={activeTask}
                  onUpdated={() => {}}
                  onDeleted={() => {}}
                  isDraggable={false}
                  isAnyTaskDragging={true}
                  listId={listId}
                  sortOption={sortOption}
                  listColor={listColor}
                  taskCounts={taskCounts}
                  taskMode={taskMode}
                  selectedTaskIds={selectedTaskIds}
                  onTaskSelectionChange={onTaskSelectionChange}
                  lastSelectedTaskId={lastSelectedTaskId}
                  forceSelected={true}
                  isInlineEditEnabled={isInlineEditEnabled}
                  activeActionIconsTaskId={activeActionIconsTaskId}
                  onActionIconsChange={onActionIconsChange}
                />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      ) : (
        // Non-draggable list for when sorted by title or due date
        items.map((task) => (
          <TaskItem
            key={task.id}
            task={task}
            onUpdated={onTaskUpdated}
            onDeleted={onTaskDeleted}
            isDraggable={false}
            isAnyTaskDragging={false}
            listId={listId}
            sortOption={sortOption}
            listColor={listColor}
            taskCounts={taskCounts}
            taskMode={taskMode}
            selectedTaskIds={selectedTaskIds}
            onTaskSelectionChange={onTaskSelectionChange}
            lastSelectedTaskId={lastSelectedTaskId}
            isInlineEditEnabled={isInlineEditEnabled}
            activeActionIconsTaskId={activeActionIconsTaskId}
            onActionIconsChange={onActionIconsChange}
            allTasks={allTasks || tasks}
            onNavigateToTask={onNavigateToTask}
            isTagFiltered={isTagFiltered}
          />
        ))
      )}

      {/* Quick Add Task Interface */}
      {showQuickAdd && onAddTaskClick && (
        <QuickAddTask
          onAddTaskClick={onAddTaskClick}
        />
      )}
    </div>
  );
}
