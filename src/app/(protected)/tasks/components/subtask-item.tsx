"use client";

import { useState, useRef, useEffect, useMemo, useCallback } from "react";
import { useUser } from "@stackframe/stack";
import { formatDateTime, hasTimeComponent } from "@/lib/date-utils";
import { Task, Tag } from "@/lib/db";
import { getContrastTextColor } from "@/lib/list-colors";
import { searchTags } from "@/app/actions/tags";
import {
  useTagsQuery,
  useOptimizedTaskTags,
  useEditTagMutation,
  useDeleteTagMutation,
  useAddTaskTagMutation,
  useRemoveTaskTagMutation,
  useCreateTagMutation,
  useEditTaskMutation,
  useRemoveTaskMutation,
  useDuplicateTaskMutation,
  useMoveTaskToListMutation,
  useListsQuery,
} from "@/lib/queries";
import { useUndoRedoContext } from "@/contexts/undo-redo-context";
import { UndoRedoAction } from "@/lib/types";
import { v4 as uuidv4 } from "uuid";
import { <PERSON><PERSON>ard, GlassCardContent } from "@/components/ui/glass-card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { Calendar, Copy, Pencil, ArrowLeftRight, Trash, Check, MoreVertical } from "lucide-react";
import { EditTaskDialog } from "./edit-task-dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

import { DateTimePicker } from "@/components/ui/datetime-picker";
import { TagPill } from "@/components/ui/tag-pill";
import { InlineTagPicker } from "@/components/ui/inline-tag-picker";

interface SubtaskItemProps {
  subtask: Task;
  parentTask: Task;
  onUpdated: (updatedTask?: Task, statusChanged?: boolean) => void;
  onDeleted: (deletedTaskId?: string) => void;
  listId: string;
  sortOption: string;
  listColor?: string | null;
  taskCounts?: Record<string, number>;
  taskMode?: "completion" | "selection";
  selectedTaskIds?: Set<string>;
  onTaskSelectionChange?: (selectedIds: Set<string>) => void;
  lastSelectedTaskId?: string | null;
  isInlineEditEnabled?: boolean;
  activeActionIconsTaskId?: string | null;
  onActionIconsChange?: (taskId: string | null) => void;
  allTasks?: Task[];
  onNavigateToTask?: (task: Task) => void;
  isTagFiltered?: boolean;
}

export function SubtaskItem({
  subtask,
  parentTask,
  onUpdated,
  onDeleted,
  listId,
  sortOption,
  listColor,
  taskCounts,
  taskMode = "completion",
  selectedTaskIds = new Set(),
  onTaskSelectionChange,
  lastSelectedTaskId = null,
  isInlineEditEnabled = true,
  activeActionIconsTaskId = null,
  onActionIconsChange,
  allTasks = [],
  onNavigateToTask,
  isTagFiltered = false,
}: SubtaskItemProps) {
  const user = useUser();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isMoreMenuOpen, setIsMoreMenuOpen] = useState(false);

  // State for inline editing
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editedTitle, setEditedTitle] = useState(subtask.title);
  const [isSaving, setIsSaving] = useState(false);

  // Refs for auto-save
  const titleInputRef = useRef<HTMLInputElement>(null);

  // TanStack Query hooks
  const { data: availableTags = [] } = useTagsQuery(user?.id || "");
  const { data: taskTags = [] } = useOptimizedTaskTags(subtask.id, listId, user?.id || "");
  const { data: availableLists = [] } = useListsQuery(user?.id || "");

  // Mutations
  const editTaskMutation = useEditTaskMutation(listId, sortOption);
  const removeTaskMutation = useRemoveTaskMutation(listId, sortOption);
  const duplicateTaskMutation = useDuplicateTaskMutation(listId, sortOption);
  const moveTaskMutation = useMoveTaskToListMutation(listId, sortOption);
  const createTagMutation = useCreateTagMutation(user?.id || "");
  const addTaskTagMutation = useAddTaskTagMutation(subtask.id, user?.id || "", listId);
  const removeTaskTagMutation = useRemoveTaskTagMutation(subtask.id, user?.id || "", listId);
  const editTagMutation = useEditTagMutation(user?.id || "");
  const deleteTagMutation = useDeleteTagMutation(user?.id || "");

  // Undo/Redo context
  const { addAction } = useUndoRedoContext();

  // Computed values
  const isCompleted = subtask.status === 'completed';
  const isGloballySelected = selectedTaskIds.has(subtask.id);
  const showActionIcons = activeActionIconsTaskId === subtask.id;

  // Focus title input when editing starts
  useEffect(() => {
    if (isEditingTitle && titleInputRef.current) {
      titleInputRef.current.focus();
      titleInputRef.current.select();
    }
  }, [isEditingTitle]);

  // Reset edited title when subtask changes
  useEffect(() => {
    setEditedTitle(subtask.title);
  }, [subtask.title]);

  const handleCheckboxClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (!user) return;

    if (taskMode === "selection") {
      // Handle selection mode
      const newSelectedIds = new Set(selectedTaskIds);
      if (isGloballySelected) {
        newSelectedIds.delete(subtask.id);
      } else {
        newSelectedIds.add(subtask.id);
      }
      onTaskSelectionChange?.(newSelectedIds);
      return;
    }

    // Handle completion mode
    const newStatus = isCompleted ? 'todo' : 'completed';
    
    try {
      setIsSaving(true);
      const result = await editTaskMutation.mutateAsync({
        taskId: subtask.id,
        userId: user.id,
        data: { status: newStatus }
      });
      
      if (result) {
        // Add undo action
        const undoAction: UndoRedoAction = {
          id: uuidv4(),
          type: 'task_status_change',
          timestamp: Date.now(),
          description: `${newStatus === 'completed' ? 'Completed' : 'Uncompleted'} subtask "${subtask.title}"`,
          taskId: subtask.id,
          listId: listId,
          previousStatus: subtask.status,
          newStatus: newStatus,
        };
        addAction(undoAction);
        
        onUpdated(result, true);
      }
    } catch (error) {
      console.error('Error updating subtask status:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveTitle = async () => {
    if (!user || editedTitle.trim() === subtask.title || !editedTitle.trim()) {
      setEditedTitle(subtask.title);
      setIsEditingTitle(false);
      return;
    }

    try {
      setIsSaving(true);
      const result = await editTaskMutation.mutateAsync({
        taskId: subtask.id,
        userId: user.id,
        data: { title: editedTitle.trim() }
      });
      
      if (result) {
        // Add undo action
        const undoAction: UndoRedoAction = {
          id: uuidv4(),
          type: 'task_edit',
          timestamp: Date.now(),
          description: `Edited subtask title`,
          taskId: subtask.id,
          listId: listId,
          previousData: { title: subtask.title },
          newData: { title: editedTitle.trim() },
        };
        addAction(undoAction);
        
        onUpdated(result, false);
      }
    } catch (error) {
      console.error('Error updating subtask title:', error);
      setEditedTitle(subtask.title);
    } finally {
      setIsSaving(false);
      setIsEditingTitle(false);
    }
  };

  const handleDuplicateTask = async () => {
    if (!user) return;

    try {
      const result = await duplicateTaskMutation.mutateAsync({
        taskId: subtask.id,
        userId: user.id,
      });

      if (result) {
        // Add undo action
        const undoAction: UndoRedoAction = {
          id: uuidv4(),
          type: 'task_duplicate',
          timestamp: Date.now(),
          description: `Duplicated subtask "${subtask.title}"`,
          originalTaskId: subtask.id,
          duplicatedTask: result,
          listId: listId,
          position: result.position,
        };
        addAction(undoAction);

        onUpdated(result, false);
      }
    } catch (error) {
      console.error('Error duplicating subtask:', error);
    }
  };

  const handleMoveToList = async (newListId: string) => {
    if (!user) return;

    try {
      const result = await moveTaskMutation.mutateAsync({
        taskId: subtask.id,
        userId: user.id,
        newListId,
      });

      if (result) {
        // Add undo action
        const undoAction: UndoRedoAction = {
          id: uuidv4(),
          type: 'task_move_list',
          timestamp: Date.now(),
          description: `Moved subtask "${subtask.title}" to another list`,
          taskId: subtask.id,
          previousListId: listId,
          newListId: newListId,
          position: result.position,
        };
        addAction(undoAction);

        onUpdated(result, false);
      }
    } catch (error) {
      console.error('Error moving subtask:', error);
    }
  };

  const handleCardClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (taskMode === "selection") {
      handleCheckboxClick(e);
    } else if (isInlineEditEnabled && !isEditingTitle) {
      setIsEditDialogOpen(true);
    }
  };

  // Get card styles with subtle glass effect for subtasks
  const getCardStyles = (): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      marginLeft: '1.5rem', // Indent subtasks
      marginTop: '0.5rem',
      opacity: isCompleted ? 0.7 : 1,
    };

    if (isGloballySelected) {
      return {
        ...baseStyles,
        borderColor: listColor || '#3b82f6',
        borderWidth: '2px',
        borderStyle: 'solid',
      };
    }

    return baseStyles;
  };

  const getBorderClass = () => {
    if (isGloballySelected) {
      return 'border-2';
    }
    return 'border border-border/50';
  };

  return (
    <>
      <GlassCard
        style={getCardStyles()}
        className={`${getBorderClass()} ${isCompleted ? 'opacity-75' : ''} group cursor-pointer`}
        intensity="subtle"
        enableGlass={true}
        onClick={handleCardClick}
      >
        <GlassCardContent className="py-1.5 pl-4 pr-10 relative">
          {/* Action Icons - Smaller for subtasks */}
          {isInlineEditEnabled && showActionIcons && (
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex flex-col gap-1 z-10">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 p-0 hover:bg-background/80"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditDialogOpen(true);
                }}
              >
                <Pencil className="h-3 w-3" />
                <span className="sr-only">Edit subtask</span>
              </Button>

              <DropdownMenu open={isMoreMenuOpen} onOpenChange={setIsMoreMenuOpen}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 p-0 hover:bg-background/80"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className="h-3 w-3" />
                    <span className="sr-only">More options</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDuplicateTask();
                      setIsMoreMenuOpen(false);
                    }}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />

                  {availableLists.filter(list => list.id !== listId).map((list) => (
                    <DropdownMenuItem
                      key={list.id}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMoveToList(list.id);
                        setIsMoreMenuOpen(false);
                      }}
                    >
                      <ArrowLeftRight className="h-4 w-4 mr-2" />
                      Move to {list.name}
                    </DropdownMenuItem>
                  ))}

                  <DropdownMenuSeparator />

                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsDeleteDialogOpen(true);
                      setIsMoreMenuOpen(false);
                    }}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}

          <div className="flex items-start gap-3 min-w-0">
            {/* Checkbox/Radio Selector */}
            <div className="flex-shrink-0 pt-0.5">
              {taskMode === "completion" ? (
                <Checkbox
                  checked={isCompleted}
                  onCheckedChange={handleCheckboxClick}
                  className="h-5 w-5 transition-all duration-300 glass-checkbox"
                  style={{
                    backgroundColor: 'transparent',
                  } as React.CSSProperties}
                  data-testid="subtask-checkbox"
                />
              ) : (
                <button
                  type="button"
                  onClick={handleCheckboxClick}
                  className="h-5 w-5 rounded-full transition-all duration-200 flex items-center justify-center focus:outline-none glass-radio-selector"
                  style={{
                    backgroundColor: 'transparent'
                  }}
                  data-testid="subtask-radio-selector"
                  aria-label={isGloballySelected ? "Deselect subtask" : "Select subtask"}
                >
                  {isGloballySelected && (
                    <div className="w-2 h-2 rounded-full glass-radio-dot" />
                  )}
                </button>
              )}
            </div>

            <div className="flex-1 min-w-0 max-w-full">
              {isEditingTitle ? (
                <div className="mb-1">
                  <Input
                    ref={titleInputRef}
                    value={editedTitle}
                    onChange={(e) => setEditedTitle(e.target.value)}
                    onBlur={handleSaveTitle}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleSaveTitle();
                      } else if (e.key === 'Escape') {
                        setEditedTitle(subtask.title);
                        setIsEditingTitle(false);
                      }
                    }}
                    className="py-0 h-6 text-sm"
                    disabled={isSaving}
                  />
                </div>
              ) : (
                <div
                  className={`text-sm font-normal break-words overflow-hidden ${isCompleted ? 'line-through text-muted-foreground' : ''} ${isInlineEditEnabled ? 'cursor-pointer' : 'cursor-default'}`}
                  onClick={() => isInlineEditEnabled && setIsEditingTitle(true)}
                >
                  {subtask.title}
                </div>
              )}

              {/* Due date and tags - smaller for subtasks */}
              {(subtask.due_date || taskTags.length > 0) && (
                <div className="flex flex-wrap items-center gap-1 mt-1">
                  {subtask.due_date && (
                    <Badge variant="outline" className="text-xs h-5 px-1.5">
                      <Calendar className="h-2.5 w-2.5 mr-1" />
                      {formatDateTime(new Date(subtask.due_date), !hasTimeComponent(new Date(subtask.due_date)))}
                    </Badge>
                  )}

                  {taskTags.map((tag) => (
                    <TagPill
                      key={tag.id}
                      tag={tag}
                      size="xs"
                      showRemove={false}
                      allowInlineEdit={false}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </GlassCardContent>
      </GlassCard>

      <EditTaskDialog
        task={subtask}
        listId={listId}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onTaskUpdated={onUpdated}
        allTasks={allTasks}
        currentTaskIndex={allTasks.findIndex(t => t.id === subtask.id)}
        onNavigateToTask={onNavigateToTask}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogTitle className="sr-only">
            Delete Subtask
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete this subtask?
            <br />
            <br />
            This action cannot be undone.
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                if (!user) return;

                try {
                  const success = await removeTaskMutation.mutateAsync({
                    taskId: subtask.id,
                    userId: user.id,
                  });

                  if (success) {
                    // Add undo action
                    const undoAction: UndoRedoAction = {
                      id: uuidv4(),
                      type: 'task_delete',
                      timestamp: Date.now(),
                      description: `Deleted subtask "${subtask.title}"`,
                      task: subtask,
                      listId: listId,
                      position: subtask.position,
                    };
                    addAction(undoAction);

                    onDeleted(subtask.id);
                  }
                } catch (error) {
                  console.error('Error deleting subtask:', error);
                }
                setIsDeleteDialogOpen(false);
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Subtask
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
