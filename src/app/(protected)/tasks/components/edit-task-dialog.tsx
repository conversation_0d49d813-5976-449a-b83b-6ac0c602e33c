"use client";

import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { useUser } from "@stackframe/stack";
import { Task, Tag, List, TaskSortOption } from "@/lib/db";
import { searchTags, fetchTaskById } from "@/app/actions/tags";
import {
  useTagsQuery,
  useOptimizedTaskTags,
  useCreateTagMutation,
  useUpdateTaskTagsMutation,
  useEditTaskMutation,
  useListsQuery,
  useTaskCountsQuery,
  useMoveTaskToListMutation,
  useEditTagMutation,
  useDeleteTagMutation,
  useSubtasksQuery,
  useReorderTasksMutation,
  useAddSubtaskMutation,
} from "@/lib/queries";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { DateTimePicker } from "@/components/ui/datetime-picker";
import { TagPicker } from "@/components/ui/tag-picker";
import { TagPill } from "@/components/ui/tag-pill";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { TaskItem } from "./task-item";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ChevronLeft, ChevronDown, Check, Plus, X } from "lucide-react";
import { getContrastTextColor } from "@/lib/list-colors";
import { useModalSwipeNavigation } from "@/hooks/use-modal-swipe-navigation";

interface EditTaskDialogProps {
  task: Task;
  listId: string; // Add listId for optimized tag loading
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTaskUpdated: (updatedTask?: Task, statusChanged?: boolean) => void;
  onMoveToList?: (taskId: string, newListId: string) => void;
  // Task navigation props
  allTasks?: Task[]; // All tasks in the current list for navigation
  currentTaskIndex?: number; // Index of current task in allTasks array
  onNavigateToTask?: (task: Task, newIndex: number) => void; // Callback to switch to a different task
}

export function EditTaskDialog({
  task,
  listId,
  open,
  onOpenChange,
  onTaskUpdated,
  onMoveToList,
  allTasks = [],
  currentTaskIndex = 0,
  onNavigateToTask,
}: EditTaskDialogProps) {
  const user = useUser();

  // Track the current task being edited internally (starts with the task prop)
  const [currentTask, setCurrentTask] = useState<Task>(task);
  const [parentTask, setParentTask] = useState<Task | null>(null);

  const [title, setTitle] = useState(currentTask.title);
  const [description, setDescription] = useState(currentTask.description || "");
  const [dueDate, setDueDate] = useState<Date | undefined | null>(
    currentTask.due_date ? new Date(currentTask.due_date) : undefined
  );
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [pendingMoveToListId, setPendingMoveToListId] = useState<string | null>(null);

  // Tag deletion state
  const [isDeleteTagDialogOpen, setIsDeleteTagDialogOpen] = useState(false);
  const [tagToDelete, setTagToDelete] = useState<Tag | null>(null);

  // Subtask creation state
  const [isAddingSubtask, setIsAddingSubtask] = useState(false);
  const [newSubtaskTitle, setNewSubtaskTitle] = useState("");

  // Refs for auto-save debouncing
  const titleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const descriptionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const dueDateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const newSubtaskInputRef = useRef<HTMLInputElement>(null);

  // Ref to track if we've initialized the dialog for this session
  const initializedRef = useRef<boolean>(false);

  // TanStack Query hooks with optimized tag loading - use current task's actual list_id
  const { data: availableTags = [] } = useTagsQuery(user?.id || "");
  const { data: taskTags = [] } = useOptimizedTaskTags(currentTask.id, currentTask.list_id, user?.id || "");
  const { data: availableLists = [] } = useListsQuery(user?.id || "");
  const { data: taskCounts = {} } = useTaskCountsQuery(user?.id || "");

  // Subtasks query - only fetch if current task is a parent task
  const { data: subtasks = [] } = useSubtasksQuery(
    currentTask.parent_task_id ? "" : currentTask.id,
    user?.id || ""
  );

  // Mutations
  const createTagMutation = useCreateTagMutation(user?.id || "");
  const updateTaskTagsMutation = useUpdateTaskTagsMutation(currentTask.id, user?.id || "", currentTask.list_id);
  const editTaskMutation = useEditTaskMutation(currentTask.list_id, 'position');
  const moveTaskMutation = useMoveTaskToListMutation(currentTask.list_id, 'position');
  const editTagMutation = useEditTagMutation(user?.id || "");
  const deleteTagMutation = useDeleteTagMutation(user?.id || "");
  const reorderTasksMutation = useReorderTasksMutation(currentTask.list_id, 'position');
  const addSubtaskMutation = useAddSubtaskMutation(currentTask.id, currentTask.list_id, 'position');

  // Task navigation logic
  const canNavigatePrevious = allTasks.length > 1 && currentTaskIndex > 0;
  const canNavigateNext = allTasks.length > 1 && currentTaskIndex < allTasks.length - 1;

  // Drag and drop sensors for subtask reordering
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Clear pending auto-save operations before navigating
  const clearPendingAutoSave = () => {
    if (titleTimeoutRef.current) {
      clearTimeout(titleTimeoutRef.current);
      titleTimeoutRef.current = null;
    }
    if (descriptionTimeoutRef.current) {
      clearTimeout(descriptionTimeoutRef.current);
      descriptionTimeoutRef.current = null;
    }
    if (dueDateTimeoutRef.current) {
      clearTimeout(dueDateTimeoutRef.current);
      dueDateTimeoutRef.current = null;
    }
  };

  const handleNavigatePrevious = () => {
    if (canNavigatePrevious && onNavigateToTask) {
      // Clear any pending auto-save operations
      clearPendingAutoSave();

      const newIndex = currentTaskIndex - 1;
      const previousTask = allTasks[newIndex];

      // Update internal current task state
      setCurrentTask(previousTask);

      onNavigateToTask(previousTask, newIndex);
    }
  };

  const handleNavigateNext = () => {
    if (canNavigateNext && onNavigateToTask) {
      // Clear any pending auto-save operations
      clearPendingAutoSave();

      const newIndex = currentTaskIndex + 1;
      const nextTask = allTasks[newIndex];

      // Update internal current task state
      setCurrentTask(nextTask);

      onNavigateToTask(nextTask, newIndex);
    }
  };

  // Navigate to a specific task (for subtask/parent navigation)
  const handleNavigateToTask = async (targetTask: Task) => {
    // Clear any pending auto-save operations
    clearPendingAutoSave();

    // Update internal current task state
    setCurrentTask(targetTask);

    // If navigating to a subtask, we don't need to update the external navigation
    // since the modal stays open and we're just changing the editing context
    // The external onNavigateToTask is only for list navigation
  };

  // Fetch parent task when current task is a subtask
  useEffect(() => {
    const fetchParentTask = async () => {
      if (currentTask.parent_task_id && user?.id) {
        try {
          const parent = await fetchTaskById(currentTask.parent_task_id, user.id);
          setParentTask(parent);
        } catch (error) {
          console.error('Error fetching parent task:', error);
          setParentTask(null);
        }
      } else {
        setParentTask(null);
      }
    };

    fetchParentTask();
  }, [currentTask.parent_task_id, user?.id]);

  // Swipe navigation for mobile
  const swipeEnabled = open && allTasks.length > 1;

  const { containerRef } = useModalSwipeNavigation({
    onSwipeLeft: handleNavigateNext,
    onSwipeRight: handleNavigatePrevious,
    threshold: 80,
    enabled: swipeEnabled,
  });

  // Initialize currentTask ONLY when dialog first opens, not when task prop changes
  useEffect(() => {
    if (open && task && task.id) {
      // Only initialize once per dialog session
      if (!initializedRef.current) {
        setCurrentTask(task);
        initializedRef.current = true;
      }
    } else if (!open) {
      // Reset initialization flag when dialog closes
      initializedRef.current = false;
    }
  }, [open, task.id]); // Only depend on open and task.id

  // Memoize taskTags to prevent infinite re-renders
  const memoizedTaskTags = useMemo(() => taskTags, [taskTags?.length, taskTags?.map(tag => tag.id).join(',')]);

  // Update form when currentTask changes (either from prop or navigation)
  useEffect(() => {
    if (open && currentTask && currentTask.id) {
      setTitle(currentTask.title);
      setDescription(currentTask.description || "");
      setDueDate(currentTask.due_date ? new Date(currentTask.due_date) : undefined);
      // Set selected tags from TanStack Query data
      setSelectedTags(memoizedTaskTags);
    }
  }, [currentTask.id, currentTask.title, currentTask.description, currentTask.due_date, open, memoizedTaskTags]);

  // Clear pending operations when task changes (navigation)
  useEffect(() => {
    // Clear any pending auto-save operations when switching tasks
    clearPendingAutoSave();

    // Reset pending move when switching tasks
    setPendingMoveToListId(null);
  }, [currentTask.id]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (titleTimeoutRef.current) clearTimeout(titleTimeoutRef.current);
      if (descriptionTimeoutRef.current) clearTimeout(descriptionTimeoutRef.current);
      if (dueDateTimeoutRef.current) clearTimeout(dueDateTimeoutRef.current);
    };
  }, []);

  const handleTagSelect = async (tag: Tag) => {
    // Prevent duplicate selection
    if (selectedTags.some(selectedTag => selectedTag.id === tag.id)) {
      console.log("Tag already selected:", tag.name);
      return;
    }

    const newTags = [...selectedTags, tag];
    setSelectedTags(newTags);

    // Auto-save tags immediately
    if (user) {
      try {
        await updateTaskTagsMutation.mutateAsync(newTags.map(t => t.id));
      } catch (error) {
        console.error("Error saving tags:", error);
        setSelectedTags(selectedTags); // Revert on error
      }
    }
  };

  const handleTagRemove = async (tag: Tag) => {
    const newTags = selectedTags.filter(t => t.id !== tag.id);
    setSelectedTags(newTags);

    // Auto-save tags immediately
    if (user) {
      try {
        await updateTaskTagsMutation.mutateAsync(newTags.map(t => t.id));
      } catch (error) {
        console.error("Error saving tags:", error);
        setSelectedTags(selectedTags); // Revert on error
      }
    }
  };

  // Auto-save functions with optimistic updates (matching inline editing pattern)
  // Use currentTask instead of task prop to ensure we're saving to the correct task
  const autoSaveTitle = async (newTitle: string) => {
    if (!user || newTitle === currentTask.title || !newTitle.trim()) return;

    try {
      const result = await editTaskMutation.mutateAsync({
        taskId: currentTask.id,
        userId: user.id,
        data: { title: newTitle.trim() }
      });
      if (result) {
        // Update internal current task state with the result
        setCurrentTask(result);
        onTaskUpdated(result, false);
      }
    } catch (error) {
      console.error("Error saving title:", error);
      setTitle(currentTask.title); // Reset on error
    }
  };

  const autoSaveDescription = async (newDescription: string) => {
    if (!user || newDescription === currentTask.description) return;

    try {
      const result = await editTaskMutation.mutateAsync({
        taskId: currentTask.id,
        userId: user.id,
        data: { description: newDescription.trim() || null }
      });
      if (result) {
        // Update internal current task state with the result
        setCurrentTask(result);
        onTaskUpdated(result, false);
      }
    } catch (error) {
      console.error("Error saving description:", error);
      setDescription(currentTask.description || ""); // Reset on error
    }
  };

  const autoSaveDueDate = async (newDueDate: Date | undefined | null) => {
    if (!user) return;

    try {
      const result = await editTaskMutation.mutateAsync({
        taskId: currentTask.id,
        userId: user.id,
        data: { due_date: newDueDate === null ? null : newDueDate }
      });
      if (result) {
        // Update internal current task state with the result
        setCurrentTask(result);
        onTaskUpdated(result, false);
      }
    } catch (error) {
      console.error("Error saving due date:", error);
      setDueDate(currentTask.due_date ? new Date(currentTask.due_date) : undefined); // Reset on error
    }
  };

  const handleTagCreate = async (name: string, color: string): Promise<Tag | null> => {
    if (!user) return null;

    try {
      const newTag = await createTagMutation.mutateAsync({ name, color });
      // TanStack Query automatically updates the available tags cache
      return newTag;
    } catch (error) {
      console.error('Error creating tag:', error);
      return null;
    }
  };

  const handleSearchTags = useCallback(async (searchTerm: string): Promise<Tag[]> => {
    if (!user) return [];

    try {
      return await searchTags(user.id, searchTerm);
    } catch (error) {
      console.error('Error searching tags:', error);
      return [];
    }
  }, [user]);

  // Tag editing handlers for inline editing
  const handleEditTag = async (tagId: string, newName: string): Promise<boolean> => {
    if (!user) return false;

    try {
      const updatedTag = await editTagMutation.mutateAsync({
        tagId,
        data: { name: newName }
      });
      if (updatedTag) {
        // Update the selected tags with the new name
        setSelectedTags(prevTags =>
          prevTags.map(tag =>
            tag.id === tagId ? { ...tag, name: newName } : tag
          )
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error editing tag:", error);
      return false;
    }
  };

  const handleDeleteTag = async (tagId: string): Promise<boolean> => {
    if (!user) return false;

    try {
      const success = await deleteTagMutation.mutateAsync(tagId);
      if (success) {
        // Remove the tag from selected tags
        setSelectedTags(prevTags => prevTags.filter(tag => tag.id !== tagId));
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error deleting tag:", error);
      return false;
    }
  };

  const handleTagDeleteClick = async (tagId: string): Promise<boolean> => {
    const tag = selectedTags.find(t => t.id === tagId);
    if (tag) {
      setTagToDelete(tag);
      setIsDeleteTagDialogOpen(true);
    }
    return false; // Don't delete immediately, wait for confirmation
  };

  const confirmDeleteTag = async () => {
    if (tagToDelete) {
      const success = await handleDeleteTag(tagToDelete.id);
      if (success) {
        setIsDeleteTagDialogOpen(false);
        setTagToDelete(null);
      }
    }
  };

  // Debounced handlers for auto-save
  const handleTitleChange = (newTitle: string) => {
    setTitle(newTitle);

    // Clear existing timeout
    if (titleTimeoutRef.current) {
      clearTimeout(titleTimeoutRef.current);
    }

    // Set new timeout for auto-save
    titleTimeoutRef.current = setTimeout(() => {
      autoSaveTitle(newTitle);
    }, 500); // 500ms debounce
  };

  const handleDescriptionChange = (newDescription: string) => {
    setDescription(newDescription);

    // Clear existing timeout
    if (descriptionTimeoutRef.current) {
      clearTimeout(descriptionTimeoutRef.current);
    }

    // Set new timeout for auto-save
    descriptionTimeoutRef.current = setTimeout(() => {
      autoSaveDescription(newDescription);
    }, 500); // 500ms debounce
  };

  const handleDueDateChange = (newDueDate: Date | undefined | null) => {
    setDueDate(newDueDate);

    // Clear existing timeout
    if (dueDateTimeoutRef.current) {
      clearTimeout(dueDateTimeoutRef.current);
    }

    // Set new timeout for auto-save
    dueDateTimeoutRef.current = setTimeout(() => {
      autoSaveDueDate(newDueDate);
    }, 300); // Shorter debounce for date picker
  };

  // Drag and drop handler for subtask reordering
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = subtasks.findIndex((task) => task.id === active.id);
    const newIndex = subtasks.findIndex((task) => task.id === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      return;
    }

    // Optimistically update the local state
    const newSubtasks = arrayMove(subtasks, oldIndex, newIndex);

    try {
      // Update positions in the database
      await reorderTasksMutation.mutateAsync({
        taskIds: newSubtasks.map(task => task.id),
        userId: user?.id || "",
      });
    } catch (error) {
      console.error('Error reordering subtasks:', error);
    }
  };

  // Subtask management functions
  const handleAddSubtask = async () => {
    if (!user || !newSubtaskTitle.trim()) return;

    try {
      const result = await addSubtaskMutation.mutateAsync({
        userId: user.id,
        title: newSubtaskTitle.trim(),
      });

      if (result) {
        setNewSubtaskTitle("");
        setIsAddingSubtask(false);
        onTaskUpdated(result, false);
      }
    } catch (error) {
      console.error('Error creating subtask:', error);
    }
  };

  // Focus effect for new subtask input
  useEffect(() => {
    if (isAddingSubtask && newSubtaskInputRef.current) {
      newSubtaskInputRef.current.focus();
    }
  }, [isAddingSubtask]);



  const handleMoveToList = (newListId: string) => {
    if (!user?.id) return;

    // Prevent moving subtasks to other lists
    if (currentTask.parent_task_id) {
      console.log("Cannot move subtask to another list");
      return;
    }

    if (newListId === listId) {
      // User selected current list - cancel any pending move
      setPendingMoveToListId(null);
    } else {
      // Store the pending move - it will be executed when the dialog closes
      setPendingMoveToListId(newListId);
    }
  };

  const handleClose = async () => {
    // Clear any pending timeouts using the helper function
    clearPendingAutoSave();

    // Close the dialog first
    onOpenChange(false);

    // Execute pending move after dialog is closed - use currentTask
    // But only if it's not a subtask
    if (pendingMoveToListId && user?.id && !currentTask.parent_task_id) {
      try {
        await moveTaskMutation.mutateAsync({
          taskId: currentTask.id,
          userId: user.id,
          newListId: pendingMoveToListId,
        });
        // TanStack Query mutation handles all cache updates automatically
      } catch (error) {
        console.error("Error moving task:", error);
      } finally {
        setPendingMoveToListId(null);
      }
    } else if (currentTask.parent_task_id) {
      // Clear any pending move for subtasks
      setPendingMoveToListId(null);
    }
  };

  // Don't render if task is invalid or missing
  if (!currentTask || !currentTask.id) {
    return null;
  }

  return (
    <MobileDialog open={open} onOpenChange={handleClose}>
      <MobileDialogContent
        className="sm:max-w-[425px]"
        fullHeight
        ref={containerRef}
      >
        <VisuallyHidden asChild>
          <MobileDialogTitle>Edit Task</MobileDialogTitle>
        </VisuallyHidden>
        <MobileDialogHeader className="flex items-start justify-between px-4 pt-4 pb-0 md:px-0 md:pt-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>

          {/* Move to List Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className={`h-8 px-3 text-sm font-medium ${
                  currentTask.parent_task_id
                    ? 'opacity-50 cursor-not-allowed'
                    : ''
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                disabled={!!currentTask.parent_task_id}
              >
                {pendingMoveToListId
                  ? availableLists.find(list => list.id === pendingMoveToListId)?.name || "Unknown List"
                  : availableLists.find(list => list.id === listId)?.name || "Unknown List"
                }
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="start"
              onClick={(e) => e.stopPropagation()}
              className="max-h-[200px] overflow-y-auto z-[80]"
            >
              {currentTask.parent_task_id ? (
                <DropdownMenuItem disabled className="text-muted-foreground text-xs">
                  Subtasks cannot be moved to other lists
                </DropdownMenuItem>
              ) : (
                availableLists
                  .map((list) => {
                  const hasColor = list.color !== null;
                  const taskCount = taskCounts?.[list.id] || 0;

                  // Get hover styles for list color with !important to override defaults
                  const getHoverStyles = (): React.CSSProperties => {
                    if (!hasColor) {
                      return {
                        '--hover-bg': '#f8f9fa',
                        '--hover-color': '#374151', // Brighter text color for better visibility
                      } as React.CSSProperties;
                    }

                    const textColor = getContrastTextColor(list.color);
                    return {
                      '--hover-bg': list.color!,
                      '--hover-color': textColor,
                    } as React.CSSProperties;
                  };

                  const getBadgeStyles = () => {
                    if (!hasColor || !list.color) {
                      return {
                        backgroundColor: "#e9ecef",
                        color: "#374151", // Brighter text color for better visibility
                        opacity: 0.8,
                      };
                    }

                    const textColor = getContrastTextColor(list.color);
                    return {
                      backgroundColor: list.color,
                      color: textColor,
                      opacity: 0.8,
                    };
                  };

                  return (
                    <DropdownMenuItem
                      key={list.id}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMoveToList(list.id);
                      }}
                      className="flex items-center gap-2 px-3 py-2 cursor-pointer transition-colors [&:hover]:bg-[var(--hover-bg)] [&:hover]:text-[var(--hover-color)]"
                      style={getHoverStyles() as React.CSSProperties}
                    >
                      {list.id === listId && (
                        <Check className="h-4 w-4 text-current" />
                      )}
                      <span className="truncate max-w-[120px] font-medium text-sm">
                        {list.name}
                      </span>
                      {taskCount > 0 && (
                        <Badge
                          className="text-xs border-transparent"
                          style={getBadgeStyles()}
                        >
                          {taskCount}
                        </Badge>
                      )}
                    </DropdownMenuItem>
                  );
                })
              )}
              {!currentTask.parent_task_id && availableLists.length === 0 && (
                <DropdownMenuItem disabled>
                  No lists available
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </MobileDialogHeader>

        <div className="grid gap-4 px-4 pb-4 md:px-0 min-w-0">
          <div className="grid gap-2 min-w-0">
            <Input
              id="edit-title"
              type="search"
              value={title}
              onChange={(e) => handleTitleChange(e.target.value)}
              placeholder="Task title"
              autoComplete="off"
              spellCheck="false"
            />
          </div>

          <div className="grid gap-2 min-w-0">
            <RichTextEditor
              id="edit-description"
              value={description}
              onChange={handleDescriptionChange}
              placeholder="Add details about this task (optional)"
            />
          </div>

          <div className="grid gap-2 min-w-0">
            <DateTimePicker
              value={dueDate}
              onChange={handleDueDateChange}
              placeholder="Set due date"
              clearable
            />
          </div>

          <div className="grid gap-2">
            {/* Selected Tags with Inline Editing */}
            {selectedTags.length > 0 && (
              <div className="flex flex-wrap gap-1.5 mb-2">
                {selectedTags.map((tag) => (
                  <TagPill
                    key={tag.id}
                    tag={tag}
                    onRemove={() => handleTagRemove(tag)}
                    onEdit={handleEditTag}
                    onDelete={handleTagDeleteClick}
                    size="sm"
                    showRemove={true}
                    allowInlineEdit={true}
                  />
                ))}
              </div>
            )}

            {/* Tag Picker for Adding New Tags */}
            <TagPicker
              selectedTags={selectedTags}
              availableTags={availableTags}
              onTagSelect={handleTagSelect}
              onTagRemove={handleTagRemove}
              onTagCreate={handleTagCreate}
              onSearchTags={handleSearchTags}
              placeholder="Search or create tags..."
              hideSelectedTags={true} // Hide selected tags since we display them above with inline editing
            />
          </div>

          {/* Subtasks Section - Only show for parent tasks (not subtasks) */}
          {!currentTask.parent_task_id && (
            <div className="grid gap-2">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                  Subtasks
                  {subtasks.length > 0 && (
                    <Badge
                      className="text-xs border-transparent px-1.5 py-0.5 text-[10px] leading-none"
                      style={{
                        backgroundColor: availableLists.find(l => l.id === currentTask.list_id)?.color || '#6b7280',
                        color: getContrastTextColor(availableLists.find(l => l.id === currentTask.list_id)?.color || '#6b7280')
                      }}
                    >
                      {subtasks.filter(s => s.status !== 'completed').length}
                    </Badge>
                  )}
                </div>
                {!isAddingSubtask && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsAddingSubtask(true)}
                    className="h-6 px-2 text-xs"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add
                  </Button>
                )}
              </div>

              {/* Existing Subtasks with Drag and Drop */}
              {subtasks.length > 0 && (
                <DndContext
                  id={`modal-subtasks-${currentTask.id}`}
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={subtasks.map(task => task.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="space-y-1">
                      {subtasks.map((subtask) => (
                        <TaskItem
                          key={subtask.id}
                          task={subtask}
                          onUpdated={onTaskUpdated}
                          onDeleted={() => onTaskUpdated(currentTask, false)}
                          isDraggable={true}
                          isAnyTaskDragging={false}
                          listId={currentTask.list_id}
                          sortOption="position"
                          listColor={availableLists.find(l => l.id === currentTask.list_id)?.color}
                          taskCounts={taskCounts}
                          taskMode="completion"
                          selectedTaskIds={new Set()}
                          onTaskSelectionChange={() => {}}
                          isInlineEditEnabled={true}
                          allTasks={subtasks}
                          onNavigateToTask={handleNavigateToTask}
                          isSubtask={false} // Don't apply subtask styling in modal
                          parentTask={currentTask}
                          isInModal={true} // New prop to indicate modal context
                        />
                      ))}
                    </div>
                  </SortableContext>
                </DndContext>
              )}

              {/* Add New Subtask - Large Inline Style */}
              {isAddingSubtask ? (
                <div className="flex items-center gap-2">
                  <Input
                    ref={newSubtaskInputRef}
                    value={newSubtaskTitle}
                    onChange={(e) => setNewSubtaskTitle(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleAddSubtask();
                      } else if (e.key === 'Escape') {
                        setNewSubtaskTitle("");
                        setIsAddingSubtask(false);
                      }
                    }}
                    placeholder="Enter subtask title..."
                    className="h-8 text-sm"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleAddSubtask}
                    disabled={!newSubtaskTitle.trim()}
                    className="h-8 w-8 p-0"
                  >
                    <Check className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setNewSubtaskTitle("");
                      setIsAddingSubtask(false);
                    }}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Card
                  className="border-2 border-muted-foreground/20 bg-muted/10 hover:bg-muted/20 transition-colors cursor-pointer"
                  onClick={() => setIsAddingSubtask(true)}
                >
                  <CardContent className="px-2 flex items-center justify-center">
                    <Plus className="h-5 w-5 text-muted-foreground" />
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Parent Task Section - Only show for subtasks */}
          {currentTask.parent_task_id && parentTask && (
            <div className="grid gap-2">
              <div className="text-sm font-medium text-muted-foreground">
                Parent Task
              </div>
              <TaskItem
                key={parentTask.id}
                task={parentTask}
                onUpdated={onTaskUpdated}
                onDeleted={() => {}}
                isDraggable={false}
                isAnyTaskDragging={false}
                listId={parentTask.list_id}
                sortOption="position"
                listColor={availableLists.find(l => l.id === parentTask.list_id)?.color}
                taskCounts={taskCounts}
                taskMode="completion"
                selectedTaskIds={new Set()}
                onTaskSelectionChange={() => {}}
                isInlineEditEnabled={true}
                allTasks={[parentTask]}
                onNavigateToTask={handleNavigateToTask}
                isSubtask={false}
              />
            </div>
          )}

        </div>
      </MobileDialogContent>

      {/* Delete Tag Confirmation Dialog */}
      <AlertDialog open={isDeleteTagDialogOpen} onOpenChange={setIsDeleteTagDialogOpen}>
        <AlertDialogContent>
          <AlertDialogTitle className="sr-only">
            Delete Tag
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the tag "{tagToDelete?.name}"?
            <br />
            <br />
            This will remove it from all tasks that use this tag.
            <br />
            <br />
            This action cannot be undone.
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteTag}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Tag
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </MobileDialog>
  );
}
